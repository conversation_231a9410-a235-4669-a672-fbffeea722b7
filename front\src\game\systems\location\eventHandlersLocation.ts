/**
 * Обработчики событий для локаций
 */

import * as React from 'react';
import { KEY_BINDS, isMouseButtonPressed, isKeyPressed } from '../../utils/constants/KeyBinds';
import { WorldMap } from '@/shared';
import { Location } from '@/shared/types/Location';
import { CameraRef } from '../interaction/eventHandlers';
import { getTileCenterOnScreen, isoToScreen, isPointInDiamond } from '@/game/utils';
import { TILE_GAP } from '@/game/utils/constants/rendering';
import { calculateShortestPath, clearPath, setPath } from '../movement';
import { calculateLocationPath } from './locationMovement';
import { Position } from '@/shared/types/Common';
import { useGameStore } from '../../store/gameStore';

/**
 * Создает обработчик клика для локаций
 */
export const createLocationClickHandler = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  setCellTarget: (cell: { isoX: number; isoY: number; tileData: any } | null) => void,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  zoom: number = 1.0
) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !cameraRef.current) return;

    // Получаем координаты клика и учитываем зум canvas scale
    const rawClickX = e.clientX - rect.left;
    const rawClickY = e.clientY - rect.top;

    // Деmasштабируем координаты клика чтобы они соответствовали базовым размерам тайлов
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const clickX = centerX + (rawClickX - centerX) / zoom;
    const clickY = centerY + (rawClickY - centerY) / zoom;

    // Получаем локацию из стейта (уже сконвертированную)
    const location = useGameStore.getState().currentLocation;

    if (!location) return;

    const locationSize = location.locationSize || { x: 10, y: 10 };
    let foundTile: { isoX: number; isoY: number; tileData: any } | null = null;

    // Проверяем все тайлы локации, чтобы найти тот, в ромб которого попал клик
    for (let isoY = 0; isoY < locationSize.y; isoY++) {
      for (let isoX = 0; isoX < locationSize.x; isoX++) {
        // Получаем экранные координаты тайла
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
        const { centerX, centerY } = getTileCenterOnScreen(
          screenX,
          screenY,
          canvasWidth,
          canvasHeight,
          cameraRef.current.x,
          cameraRef.current.y
        );

        const halfTileW = tileWidth / 2 - TILE_GAP;
        const halfTileH = tileHeight / 2 - TILE_GAP;

        // Проверяем, попал ли клик в ромб этого тайла
        if (isPointInDiamond(clickX, clickY, centerX, centerY, halfTileW, halfTileH)) {
          const locationTileKey = `${isoX},${isoY}`;
          const locationTileData = location.locationMap?.[locationTileKey];
          foundTile = { isoX, isoY, tileData: locationTileData };
          break;
        }
      }
      if (foundTile) break;
    }

    if (foundTile) {
      // Проверяем, кликнули ли мы на уже выбранный тайл
      if (cellTarget && cellTarget.isoX === foundTile.isoX && cellTarget.isoY === foundTile.isoY) {
        // Отменяем выбор
        setCellTarget(null);
        clearPath();
      } else {
        // Выбираем новый тайл
        setCellTarget(foundTile);
        
        console.log(foundTile.isoX, foundTile.isoY, foundTile.tileData);
        
        
        // Вычисляем путь от текущей позиции игрока до выбранной точки в локации
        const playerPosition = currentWorld?.player?.position || { x: Math.floor(locationSize.x / 2), y: Math.floor(locationSize.y / 2) };
        const targetPosition = { x: foundTile.isoX, y: foundTile.isoY };
        
        // Используем функцию расчета пути для локаций
        const path = calculateLocationPath(location, playerPosition, targetPosition);
        if (path.length > 0) {
          // Устанавливаем путь для отображения
          setPath(path);
        } 
      }
    }
  };
};

/**
 * Создает обработчики для правого клика в локации
 */
export const createLocationRightClickHandlers = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  zoom: number = 1.0,
  onContextMenu: (x: number, y: number, tileData: { isoX: number; isoY: number; tileData: any }) => void
) => {
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    // ничего не делаем
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (e.button === 2) {
      showContextMenu(e);
    }
  };

  const showContextMenu = (e: React.MouseEvent<HTMLCanvasElement>) => {
    
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !cameraRef.current) return;

    // Получаем координаты клика и учитываем зум canvas scale
    const rawClickX = e.clientX - rect.left;
    const rawClickY = e.clientY - rect.top;

    // Деmasштабируем координаты клика чтобы они соответствовали базовым размерам тайлов
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const clickX = centerX + (rawClickX - centerX) / zoom;
    const clickY = centerY + (rawClickY - centerY) / zoom;

    // Получаем локацию из стейта (уже сконвертированную)
    const location = useGameStore.getState().currentLocation;

    if (!location) return;

    const locationSize = location.locationSize || { x: 10, y: 10 };
    let foundTile: { isoX: number; isoY: number; tileData: any } | null = null;

    // Проверяем все тайлы локации, чтобы найти тот, в ромб которого попал клик
    for (let isoY = 0; isoY < locationSize.y; isoY++) {
      for (let isoX = 0; isoX < locationSize.x; isoX++) {
        // Получ��ем экранные координаты тайла
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
        const { centerX, centerY } = getTileCenterOnScreen(
          screenX,
          screenY,
          canvasWidth,
          canvasHeight,
          cameraRef.current.x,
          cameraRef.current.y
        );

        const halfTileW = tileWidth / 2 - TILE_GAP;
        const halfTileH = tileHeight / 2 - TILE_GAP;

        // Проверяем, попал ли клик в ромб этого тайла
        if (isPointInDiamond(clickX, clickY, centerX, centerY, halfTileW, halfTileH)) {
          const locationTileKey = `${isoX},${isoY}`;
          const locationTileData = location.locationMap?.[locationTileKey];
          foundTile = { isoX, isoY, tileData: locationTileData };
          break;
        }
      }
      if (foundTile) break;
    }

    if (foundTile) {
      // Показываем контекстное меню в позиции клика
      onContextMenu(e.clientX, e.clientY, foundTile);
    }
  };

  const handleContextMenu = (e: React.MouseEvent<HTMLCanvasElement>) => {
    e.preventDefault(); // Всегда от��лючаем стандартное контекстное меню
  };

  return {
    handleMouseDown,
    handleMouseUp,
    handleContextMenu
  };
};

/**
 * Создает обработчик клавиш для локаций (вход и выход)
 */
export function createLocationKeyDownHandler(
  currentWorld?: WorldMap | null
) {
  return function handleKeyDown(e: KeyboardEvent) {
    
    // Обработка пробела для входа в локацию
    if (isKeyPressed(e, KEY_BINDS.ENTER_LOCATION) && currentWorld) {
      // Получаем текущее состояние игрока из store
      import('../../store/gameStore').then(({ useGameStore }) => {
        const { playerLocationPresent } = useGameStore.getState();
        
        // ТОЛЬКО если игрок НЕ в локации
        if (!playerLocationPresent) {
          const playerPosition = currentWorld?.player?.position;
          if (!playerPosition) return;
          
          const tileKey = `${playerPosition.x},${playerPosition.y}`;
          const currentTile = currentWorld.worldMap?.[tileKey];
          
          
          if (currentTile?.location && !currentTile.location.playerPresent) {
            
            // Сначала включаем индикатор загрузки
            const { setLocationLoading } = useGameStore.getState();
            setLocationLoading(true);
            
            // Импортируем систему входа в локацию (асинхронную версию)
            import('./locationEnterEscapeLogic').then(async ({ enterLocationAsync }) => {
              try {
                const updatedWorld = await enterLocationAsync(currentWorld, playerPosition);
                
                if (updatedWorld) {
                  const { setPlayerLocationPresent, setCurrentWorld } = useGameStore.getState();
                
                setPlayerLocationPresent(true);
                setCurrentWorld(updatedWorld);
                
                // Сбрасываем путь при входе в локацию
                import('../movement/playerMovement').then(({ clearPath }) => {
                  clearPath();
                });
                
                // Перемещаем камеру на игрока после входа в локацию
                setTimeout(() => {
                  const event = new CustomEvent('centerCameraOnPlayer');
                  window.dispatchEvent(event);
                }, KEY_BINDS.CAMERA_CENTER_DELAY);
              } else {
                setLocationLoading(false);
              }
              } catch (error) {
                console.error('[ENTER] Ошибка при входе в локацию:', error);
                setLocationLoading(false);
              }
            });
          } else if (currentTile?.location?.playerPresent) {
          } else {
          }
        } else {
        }
      });
    }
    
    // Обработка Escape для выхода из локации
    if (isKeyPressed(e, KEY_BINDS.EXIT_LOCATION)) {
      
      // Получаем текущее состояние игрока из store
      import('../../store/gameStore').then(({ useGameStore }) => {
        const { playerLocationPresent } = useGameStore.getState();
        
        // ТОЛЬКО если игрок В локации
        if (playerLocationPresent) {
          
          // Сначала включаем индикатор загрузки
          const { setLocationLoading } = useGameStore.getState();
          setLocationLoading(true);
          
          // Импортируем систему выхода из локации (асинхронную версию)
          import('./locationEnterEscapeLogic').then(async ({ exitLocationAsync, canExitLocation }) => {
            try {
              // Получаем актуальную позицию игрока из currentWorld
              const actualPlayerPosition = currentWorld?.player?.position;
              if (!actualPlayerPosition) {
                setLocationLoading(false);
                return;
              }
              
              // Получаем currentLocation из стейта
              const currentLocation = useGameStore.getState().currentLocation;
              
              // Проверяем, может ли игрок выйти, используя актуальную позицию из currentLocation
              let canExit = false;
              if (currentLocation?.playerPosition) {
                const currentLocationTileKey = `${currentLocation.playerPosition.x},${currentLocation.playerPosition.y}`;
                const currentLocationTile = currentLocation.locationMap?.[currentLocationTileKey];
                canExit = !!currentLocationTile?.goBackZone;
              }
              
              if (currentWorld && canExit && currentLocation?.playerPosition) {
                const result = await exitLocationAsync(currentWorld, currentLocation.playerPosition, currentLocation);
              
              if (result.success && result.updatedWorld) {
                const { setPlayerLocationPresent, setCurrentWorld, setCurrentLocation } = useGameStore.getState();
                
                setPlayerLocationPresent(false);
                setCurrentWorld(result.updatedWorld!);
                // Очищаем currentLocation из стейта
                setCurrentLocation(null);
                
                // Сбрасываем путь при выходе из локации
                import('../movement/playerMovement').then(({ clearPath }) => {
                  clearPath();
                });
                
                // Перемещаем камеру на игрока после выхода из локации
                setTimeout(() => {
                  const event = new CustomEvent('centerCameraOnPlayer');
                  window.dispatchEvent(event);
                }, 20);
              } else {
              }
            } else {
              setLocationLoading(false);
            }
            } catch (error) {
              console.error('[EXIT] Ошибка при выходе из локации:', error);
              setLocationLoading(false);
            }
          });
        } else {
        }
      });
    }
  };
}
/**
 * Движок отрисовки интерьеров локаций в изометрическом стиле
 * Полностью скопирован и адаптирован под Location из WorldMap рендера
 */

import * as React from 'react';
import { WorldMap } from '../../../shared/types/World';
import { Location } from '../../../shared/types/Location';
import { isoToScreen } from '../../utils/coordinates/isometric';
import { drawLocationTile, drawLocationTileDecorations, locationDecorationTextureManager } from './renderUtilsLocInterior';
import { CameraRef } from '../../systems/interaction/eventHandlers';
import { getCurrentPath, highlightPathCell } from '../../systems/movement/playerMovement';
import { drawPlayerSkeleton } from '../animations/animationSystem';
import { GameTime, getLightColor, getLightLevel } from '../../utils/time/gameTime';
import {  getDarknessLevel } from '../../systems/location/visionSystem';
/**
 * Основная функция отрисовки интерьера локации
 */
export const createLocationDrawFunction = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  zoom: number = 1.0,
  currentLocation: Location | null = null,
  gameTime?: GameTime
) => {
  let texturesPreloaded = false;
  
  return () => {
    const canvas = canvasRef.current;
    if (!canvas || !cameraRef.current) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Предзагружаем текстуры локаций только один раз
    if (!texturesPreloaded) {
      locationDecorationTextureManager.preloadAllTextures();
      texturesPreloaded = true;
    }

    // Сохраняем состояние контекста
    ctx.save();

    ctx.imageSmoothingEnabled = false;

    // Очищаем канвас
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Применяем зум
    ctx.translate(canvasWidth / 2, canvasHeight / 2);
    ctx.scale(zoom, zoom);
    ctx.translate(-canvasWidth / 2, -canvasHeight / 2);

    // Используем позицию игрока из currentWorld для анимации (как было)
    const playerPos = currentWorld?.player?.position;
    if (!playerPos || !currentWorld?.worldMap) {
      ctx.restore();
      return;
    }

    // Получаем локацию из параметра (уже сконвертированную)
    if (!currentLocation) {
      
      return;
    }

    const location = currentLocation;

    const locationSize = location.locationSize || { x: 10, y: 10 };

    // Центр экрана
    const centerX = cameraRef.current.x;
    const centerY = cameraRef.current.y;

    // Собираем видимые тайлы для оптимизации
    const visibleTileKeys = new Set<string>();

    // ЭТАП 1: ОТРИСОВЫВАЕМ ВСЕ ТАЙЛЫ БЕЗ ДЕКОРАЦИЙ (ФОН + МЕСТНОСТЬ)
    for (let isoY = 0; isoY < locationSize.y; isoY++) {
      for (let isoX = 0; isoX < locationSize.x; isoX++) {
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);

        const worldX = screenX + canvasWidth / 2 - centerX;
        const worldY = screenY + canvasHeight / 2 - centerY;

        // Проверяем, находится ли тайл в видимой области
        if (worldX >= -tileWidth && worldX <= canvasWidth + tileWidth && worldY >= -tileHeight && worldY <= canvasHeight + tileHeight) {
          const tileKey = `${isoX},${isoY}`;
          visibleTileKeys.add(tileKey);

          // Получаем уровень затемнения для terrain текстур
          let darknessLevel = 0;
          if (gameTime && currentWorld?.player?.parameters?.Perception) {
            darknessLevel = getDarknessLevel(
              currentWorld.player.position,
              { x: isoX, y: isoY },
              currentWorld.player.parameters.Perception,
              gameTime,
              location
            );
          }

          // Отрисовываем тайл БЕЗ персонажа И БЕЗ декораций с передачей darknessLevel
          drawLocationTile(ctx, screenX, screenY, isoX, isoY, tileWidth, tileHeight, canvasWidth, canvasHeight, centerX, centerY, location, cellTarget, false, false, darknessLevel);
        }
      }
    }

    // Получаем позицию игрока и определяем анимацию
    let animationName = 'scratching-right';
    
    if (playerPos && window.__playerIsMoving && window.__playerMoveDirection && currentWorld?.player?.position) {
      // Логика анимации (как было раньше)
      const { dx, dy } = window.__playerMoveDirection;
      
      const playerX = currentWorld.player.position.x;
      const playerY = currentWorld.player.position.y;

      const targetX = playerX + dx;
      const targetY = playerY + dy;
      
      const playerScreen = isoToScreen(playerX, playerY, tileWidth, tileHeight);
      const targetScreen = isoToScreen(targetX, targetY, tileWidth, tileHeight);
      
      const screenDeltaX = targetScreen.x - playerScreen.x;
      const screenDeltaY = targetScreen.y - playerScreen.y;
      
      const angle = Math.atan2(screenDeltaY, screenDeltaX);
      
      let normalizedAngle = angle < 0 ? angle + 2 * Math.PI : angle;
      
      let adjustedAngle = normalizedAngle - Math.PI / 2;
      if (adjustedAngle < 0) adjustedAngle += 2 * Math.PI;
      
      const degrees = (adjustedAngle * 180) / Math.PI;
      
      let skewYCorrection = 0;
      const maxSkewIntensity = 0.14;
      
      if ((degrees >= 335 && degrees <= 360) || (degrees >= 0 && degrees < 25)) {
        animationName = 'walk_south';
        window.__playerDirection = 'south';
        skewYCorrection = 0;
      } else if (degrees >= 25 && degrees < 90) {
        animationName = 'walk_south_west';
        window.__playerDirection = 'south';
        const distanceFromSouth = Math.min(degrees - 25, 65) / 65;
        skewYCorrection = distanceFromSouth * maxSkewIntensity;
      } else if (degrees >= 90 && degrees < 155) {
        animationName = 'walk_north_west';
        window.__playerDirection = 'west';
        skewYCorrection = maxSkewIntensity;
      } else if (degrees >= 155 && degrees < 205) {
        animationName = 'walk_north';
        window.__playerDirection = 'north';
        skewYCorrection = 0;
      } else if (degrees >= 205 && degrees < 270) {
        animationName = 'walk_north_east';
        window.__playerDirection = 'north';
        const distanceFromNorth = Math.min(degrees - 205, 65) / 65;
        skewYCorrection = -distanceFromNorth * maxSkewIntensity;
      } else if (degrees >= 270 && degrees < 335) {
        animationName = 'walk_south_east';
        window.__playerDirection = 'east';
        skewYCorrection = -maxSkewIntensity;
      }
      
      window.__playerSkewYCorrection = skewYCorrection;
    } else {
      window.__playerDirection = 'idle';
    }

    // ЭТАП 2-3: ОТРИСОВЫВАЕМ ДЕКОРАЦИИ И ПЕРСОНАЖЕЙ В ПРАВИЛЬНОМ ПОРЯДКЕ
    for (let isoY = 0; isoY < locationSize.y; isoY++) {
      for (let isoX = 0; isoX < locationSize.x; isoX++) {
        const tileKey = `${isoX},${isoY}`;
        
        // Рендерим только видимые тайлы с декорациями
        if (visibleTileKeys.has(tileKey)) {
          const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
          
          // Получаем уровень затемнения для передачи в функции рендеринга
          let darknessLevel = 0;
          if (gameTime && currentWorld?.player?.parameters?.Perception) {
            darknessLevel = getDarknessLevel(
              currentWorld.player.position,
              { x: isoX, y: isoY },
              currentWorld.player.parameters.Perception,
              gameTime,
              location
            );
          }

          // Отрисовываем ТОЛЬКО декорации с передачей darknessLevel
          drawLocationTileDecorations(ctx, screenX, screenY, isoX, isoY, tileWidth, tileHeight, canvasWidth, canvasHeight, centerX, centerY, location, cellTarget, darknessLevel);

          // Если игрок находится на этом тайле, рисуем его ПОСЛЕ декораций
          if (playerPos && playerPos.x === isoX && playerPos.y === isoY) {
            if (typeof drawPlayerSkeleton === 'function') {
              drawPlayerSkeleton(ctx, tileWidth, tileHeight, canvasWidth, canvasHeight, centerX, centerY, currentWorld, animationName);
            }
          }
        }
      }
    }

    // ЭТАП 4: ОТРИСОВЫВАЕМ UI ЭЛЕМЕНТЫ (пути, маркеры и т.д.)
    
    // Рис��ем центральную точку для ориентации
    ctx.fillStyle = '#ff353500';
    ctx.beginPath();
    ctx.arc(canvasWidth / 2, canvasHeight / 2, 3, 0, 2 * Math.PI);
    ctx.fill();

    // Рисуем крестики для пути персонажа (всегда показываем в локации)
    const currentPath = getCurrentPath();
    for (const pathPos of currentPath) {
      const { x: screenX, y: screenY } = isoToScreen(pathPos.x, pathPos.y, tileWidth, tileHeight);
      const worldX = screenX + canvasWidth / 2 - centerX;
      const worldY = screenY + canvasHeight / 2 - centerY;

      // Рисуем крестик только если тайл видим
      if (worldX >= -tileWidth && worldX <= canvasWidth + tileWidth && worldY >= -tileHeight && worldY <= canvasHeight + tileHeight) {
        highlightPathCell(ctx, worldX, worldY, tileWidth, tileHeight);
      }
    }

    // Восстанавливаем состояние контекста
    ctx.restore();

    // ЭТАП 5: ПРИМЕНЯЕМ ЦВЕТОВОЙ ФИЛЬТР ВРЕМЕНИ ДНЯ (В САМОМ КОНЦЕ!)
    if (gameTime) {
      const lightColor = getLightColor(gameTime);
      
      // Применяем цветовой фильтр только если есть альфа (прозрачность)
      if (lightColor.a > 0) {
        // Сохраняем состояние для фильтра
        ctx.save();
        
        // Пробуем разные режимы в зависимости от времени дня
        const lightLevel = getLightLevel(gameTime);
        
        if (lightLevel > 0.6) {
          // Дневные цвета - используем soft-light для тонкого эффекта
          ctx.globalCompositeOperation = 'soft-light';
        } else {
          // Ночные цвета - используем multiply для более сильного эффекта
          ctx.globalCompositeOperation = 'multiply';
        }
        
        ctx.fillStyle = `rgba(${lightColor.r}, ${lightColor.g}, ${lightColor.b}, ${lightColor.a})`;
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        
        // Восстанавливаем состояние
        ctx.restore();
      }
    }
  };
};
/**
 * Система отрисовки текстур декораций с поддержкой decorationSides
 */

import { LocationDecorations, TerrainType } from '../../../shared/enums';
import { loadLocationTexture } from '../textures/locationTextures';

// Кэш загруженных текстур для decorationSides
const sideTextureCache = new Map<string, HTMLImageElement>();

/**
 * Получает детерминированную папку на основе координат
 * Используется для стабильного выбора папки с текстурами
 */
function getDeterministicFolder(x: number, y: number): number {
  // Используем формулу π для детерминированного выбора папки
  const p = (x * Math.PI * 0.618 + y * Math.PI * 1.414);
  const hash = Math.abs(Math.sin(p * 1.732) * Math.cos(p / 2.236) * 10000);
  // Пока что возвращаем папку 1, в будущем будет больше папок
  return 1;
}

/**
 * Получает материал текстуры на основе terrain тайла
 */
function getTextureMaterial(terrain: TerrainType): string {
  switch (terrain) {
    case TerrainType.BETON:
      return 'beton';
    case TerrainType.WOOD:
      return 'wood';
    case TerrainType.METAL:
      return 'metal';
    default:
      return 'beton'; // По умолчанию бетон
  }
}

/**
 * Загружает текстуру стороны декорации и кэширует её
 */
async function loadSideTexture(src: string): Promise<HTMLImageElement> {
  // Проверяем кэш
  if (sideTextureCache.has(src)) {
    return sideTextureCache.get(src)!;
  }

  // Загружаем новую текстуру
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      sideTextureCache.set(src, img);
      resolve(img);
    };
    img.onerror = () => {
      console.warn(`Не удалось загрузить текстуру стороны декорации: ${src}`);
      // Создаем заглушку
      const placeholder = new Image(1, 1);
      sideTextureCache.set(src, placeholder);
      resolve(placeholder);
    };
    img.src = src;
  });
}

/**
 * Получает пути к текстурам для decorationSides
 */
export function getSideTexturePaths(
  decoration: LocationDecorations,
  decorationSides: number[],
  terrain: TerrainType,
  x: number,
  y: number
): string[] {
  // Пока что поддерживаем только стены
  if (decoration !== LocationDecorations.WALL) {
    return [];
  }

  const textureMaterial = getTextureMaterial(terrain);
  const deterministicFolder = getDeterministicFolder(x, y);

  return decorationSides.map(sideNumber =>
    `/textures/Location/decorations/wall/${textureMaterial}/${deterministicFolder}/${sideNumber}.png`
  );
}

/**
 * Загружает все текстуры для decorationSides
 */
export async function loadSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  terrain: TerrainType,
  x: number,
  y: number
): Promise<HTMLImageElement[]> {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, terrain, x, y);

  const loadPromises = texturePaths.map(path => loadSideTexture(path));

  try {
    return await Promise.all(loadPromises);
  } catch (error) {
    console.error('Ошибка при загрузке текстур сторон декорации:', error);
    return [];
  }
}

/**
 * Синхронно получает загруженные текстуры для decorationSides
 */
export function getLoadedSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  terrain: TerrainType,
  x: number,
  y: number
): HTMLImageElement[] {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, terrain, x, y);

  return texturePaths
    .map(path => sideTextureCache.get(path))
    .filter((img): img is HTMLImageElement => img !== undefined && img.complete);
}

/**
 * Отрисовывает текстуры decorationSides на тайле
 */
export function drawSideTextures(
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  decoration: LocationDecorations,
  decorationSides: number[],
  terrain: TerrainType,
  x: number,
  y: number,
  darknessLevel: number = 0
): void {
  const textures = getLoadedSideTextures(decoration, decorationSides, terrain, x, y);

  if (textures.length === 0) {
    // Если текстуры не загружены, запускаем загрузку асинхронно
    loadSideTextures(decoration, decorationSides, terrain, x, y);
    return;
  }

  // Отрисовываем каждую текстуру с наслоением
  textures.forEach((texture, index) => {
    if (!texture || !texture.complete) {
      return;
    }

    ctx.save();

    // Применяем затемнение если нужно
    if (darknessLevel > 0) {
      const minVisibility = 0.1;
      ctx.globalAlpha = Math.max(minVisibility, 1 - darknessLevel);
    }

    // Вычисляем размеры текстуры
    const drawW = texture.width;
    const drawH = texture.height;

    // Отрисовываем текстуру по центру тайла
    ctx.drawImage(
      texture,
      centerX - drawW / 2,
      centerY - drawH / 2,
      drawW,
      drawH
    );

    ctx.restore();
  });
}

/**
 * Предзагружает текстуры для всех возможных комбинаций decorationSides
 */
export async function preloadSideTextures(): Promise<void> {
  const materials = ['beton', 'wood', 'metal'];
  const folders = [1]; // Пока что только папка 1
  const sideNumbers = [1, 2, 3, 4]; // Возможные номера текстур

  const allTexturePaths: string[] = [];

  materials.forEach(material => {
    folders.forEach(folder => {
      sideNumbers.forEach(sideNumber => {
        allTexturePaths.push(
          `/textures/Location/decorations/wall/${material}/${folder}/${sideNumber}.png`
        );
      });
    });
  });

  const loadPromises = allTexturePaths.map(path => loadSideTexture(path));

  try {
    await Promise.all(loadPromises);
    console.log('Все текстуры decorationSides предзагружены');
  } catch (error) {
    console.error('Ошибка при предзагрузке текстур decorationSides:', error);
  }
}
/**
 * Система отрисовки текстур декораций с поддержкой decorationSides
 */

import { LocationDecorations, TerrainType } from '../../../shared/enums';
import { loadLocationTexture } from '../textures/locationTextures';
import { DECORATION_TEXTURE_SETTINGS } from '../../utils/constants/renderingLocation';

// Кэш загруженных текстур для decorationSides
const sideTextureCache = new Map<string, HTMLImageElement>();

// Константы для затемнения текстур (из renderUtilsLocInterior.ts)
const DARKNESS_OPACITY = {
  MIN_VISIBILITY: 0.1, // Минимальная видимость в полной темноте
  CALCULATE_ALPHA: (darknessLevel: number) => Math.max(DARKNESS_OPACITY.MIN_VISIBILITY, 1 - darknessLevel)
} as const;

/**
 * Получает детерминированную папку на основе координат
 * Используется для стабильного выбора папки с текстурами
 */
function getDeterministicFolder(x: number, y: number): number {
  // Используем формулу π для детерминированного выбора папки
  const p = (x * Math.PI * 0.618 + y * Math.PI * 1.414);
  const hash = Math.abs(Math.sin(p * 1.732) * Math.cos(p / 2.236) * 10000);
  // Пока что возвращаем папку 1, в будущем будет больше папок
  return 1;
}

/**
 * Получает материал текстуры из данных локации или fallback на основе terrain
 */
function getTextureMaterial(locationData: any, terrain?: TerrainType): string {
  // Сначала пытаемся взять textureMaterial из данных локации
  if (locationData?.textureMaterial) {
    return locationData.textureMaterial;
  }

  // Fallback на основе terrain если textureMaterial не указан
  if (terrain) {
    switch (terrain) {
      case TerrainType.BETON:
        return 'beton';
      case TerrainType.WOOD:
        return 'wood';
      case TerrainType.METAL:
        return 'metal';
      default:
        return 'beton';
    }
  }

  return 'beton'; // По умолчанию бетон
}

/**
 * Загружает текстуру стороны декорации и кэширует её
 */
async function loadSideTexture(src: string): Promise<HTMLImageElement> {
  // Проверяем кэш
  if (sideTextureCache.has(src)) {
    return sideTextureCache.get(src)!;
  }

  // Загружаем новую текстуру
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      sideTextureCache.set(src, img);
      resolve(img);
    };
    img.onerror = () => {
      console.warn(`Не удалось загрузить текстуру стороны декорации: ${src}`);
      // Создаем заглушку
      const placeholder = new Image(1, 1);
      sideTextureCache.set(src, placeholder);
      resolve(placeholder);
    };
    img.src = src;
  });
}

/**
 * Получает пути к текстурам для decorationSides
 */
export function getSideTexturePaths(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  terrain?: TerrainType,
  x?: number,
  y?: number
): string[] {
  // Пока что поддерживаем только стены
  if (decoration !== LocationDecorations.WALL) {
    return [];
  }

  const textureMaterial = getTextureMaterial(locationData, terrain);
  const deterministicFolder = getDeterministicFolder(x || 0, y || 0);

  // Обрабатываем decorationSides правильно - каждый элемент может быть числом или массивом
  const texturePaths: string[] = [];

  decorationSides.forEach(sideItem => {
    if (Array.isArray(sideItem)) {
      // Если элемент - массив, добавляем все текстуры из него
      sideItem.forEach(sideNumber => {
        texturePaths.push(
          `/textures/Location/decorations/wall/${textureMaterial}/${deterministicFolder}/${sideNumber}.png`
        );
      });
    } else {
      // Если элемент - число, добавляем одну текстуру
      texturePaths.push(
        `/textures/Location/decorations/wall/${textureMaterial}/${deterministicFolder}/${sideItem}.png`
      );
    }
  });

  return texturePaths;
}

/**
 * Загружает все текстуры для decorationSides
 */
export async function loadSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  terrain?: TerrainType,
  x?: number,
  y?: number
): Promise<HTMLImageElement[]> {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, locationData, terrain, x, y);

  const loadPromises = texturePaths.map(path => loadSideTexture(path));

  try {
    return await Promise.all(loadPromises);
  } catch (error) {
    console.error('Ошибка при загрузке текстур сторон декорации:', error);
    return [];
  }
}

/**
 * Синхронно получает загруженные текстуры для decorationSides
 */
export function getLoadedSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  terrain?: TerrainType,
  x?: number,
  y?: number
): HTMLImageElement[] {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, locationData, terrain, x, y);

  return texturePaths
    .map(path => sideTextureCache.get(path))
    .filter((img): img is HTMLImageElement => img !== undefined && img.complete);
}

/**
 * Отрисовывает текстуры decorationSides на тайле
 */
export function drawSideTextures(
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  terrain?: TerrainType,
  x?: number,
  y?: number,
  darknessLevel: number = 0
): void {
  const textures = getLoadedSideTextures(decoration, decorationSides, locationData, terrain, x, y);

  if (textures.length === 0) {
    // Если текстуры не загружены, запускаем загрузку асинхронно
    loadSideTextures(decoration, decorationSides, locationData, terrain, x, y);
    return;
  }

  // Отрисовываем каждую текстуру с наслоением
  textures.forEach((texture, index) => {
    if (!texture || !texture.complete) {
      return;
    }

    ctx.save();

    // Применяем затемнение если нужно
    if (darknessLevel > 0) {
      ctx.globalAlpha = DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
    }

    // Вычисляем размеры текстуры с учетом настроек из констант
    let drawW = texture.width;
    let drawH = texture.height;

    if (DECORATION_TEXTURE_SETTINGS.ENABLE_SCALING) {
      const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS.WALL || 1.0;

      drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
      drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;

      if (DECORATION_TEXTURE_SETTINGS.PRESERVE_ASPECT) {
        const aspectRatio = texture.width / texture.height;
        if (aspectRatio > 1) {
          drawH = drawW / aspectRatio;
        } else {
          drawW = drawH * aspectRatio;
        }
      }
    }

    // Вычисляем смещение позиции из констант
    let offsetX = 0;
    let offsetY = 0;

    if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
      offsetX = DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET.WALL || 0;
      offsetY = DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET.WALL || 0;
    }

    // Отрисовываем текстуру с учетом размеров и смещений из констант
    ctx.drawImage(
      texture,
      centerX - drawW / 2 + offsetX,
      centerY - drawH / 2 + offsetY,
      drawW,
      drawH
    );

    ctx.restore();
  });
}

/**
 * Предзагружает текстуры для всех возможных комбинаций decorationSides
 */
export async function preloadSideTextures(): Promise<void> {
  const materials = ['beton', 'wood', 'metal'];
  const folders = [1]; // Пока что только папка 1
  const sideNumbers = [1, 2, 3, 4]; // Возможные номера текстур

  const allTexturePaths: string[] = [];

  materials.forEach(material => {
    folders.forEach(folder => {
      sideNumbers.forEach(sideNumber => {
        allTexturePaths.push(
          `/textures/Location/decorations/wall/${material}/${folder}/${sideNumber}.png`
        );
      });
    });
  });

  const loadPromises = allTexturePaths.map(path => loadSideTexture(path));

  try {
    await Promise.all(loadPromises);
    console.log('Все текстуры decorationSides предзагружены');
  } catch (error) {
    console.error('Ошибка при предзагрузке текстур decorationSides:', error);
  }
}
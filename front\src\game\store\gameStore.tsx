import { Position, WorldMap, WorldSummary } from '@/shared'
import type { Location } from '../../shared/types/Location'
import { locationToTransferLocation } from '../systems/location/locationConverter'
import { create } from 'zustand'
import { GameTime } from '../utils/time/gameTime'
import { gameTimeManager } from '../utils/time/gameTimeManager'


interface GameSettings {
  volume: number
  soundEnabled: boolean
  musicEnabled: boolean
  difficulty: 'easy' | 'normal' | 'hard'
  autoSave: boolean
  worldMapPlayerSpeed: number // 1-5, где 1=200ms, 5=2000ms
}

interface GameStoreState {
  worldSummaries: WorldSummary[]
  currentWorld: WorldMap | null
  currentWorldId: string | null
  settings: GameSettings
  playerLocationPresent: boolean
  currentLocation: Location | null  // Текущая локация
  locationLoading: boolean  // Индикатор загрузки локации
  gameTime: GameTime | null  // Игровое время
  setWorldSummaries: (worlds: WorldSummary[]) => void
  setCurrentWorld: (world: WorldMap | null) => void
  setCurrentWorldId: (worldId: string | null) => void
  updateSettings: (newSettings: Partial<GameSettings>) => void
  setPlayerLocationPresent: (present: boolean) => void
  setCurrentLocation: (location: Location | null) => void  // Сеттер для локации
  setLocationLoading: (loading: boolean) => void  // Сеттер для загрузки
  updatePlayerPositionInLocation: (newPosition: Position) => void  // Метод для обновления позиции в локации
  syncLocationToWorld: () => Promise<void>  // Синхронизация currentLocation с currentWorld перед сохранением
  loadFullWorld: (worldId: string, userId: string) => Promise<void>
  setGameTime: (time: GameTime) => void  // Сеттер для игрового времени
}

const defaultSettings: GameSettings = {
  volume: 0.7,
  soundEnabled: true,
  musicEnabled: true,
  difficulty: 'normal',
  autoSave: true,
  worldMapPlayerSpeed: 3 // По умолчанию средняя скорость (3 из 5)
}

export const useGameStore = create<GameStoreState>((set, get) => ({
  worldSummaries: [],
  currentWorld: null,
  currentWorldId: null,
  settings: defaultSettings,
  playerLocationPresent: false,
  currentLocation: null,  // Текущая локация
  locationLoading: false,  // Индикатор загрузки
  gameTime: null,  // Игровое время
  setWorldSummaries: (worlds: WorldSummary[]) => set({ worldSummaries: worlds }),
  setCurrentWorld: (world: WorldMap | null) => set({ currentWorld: world }),
  setCurrentWorldId: (worldId: string | null) => set({ currentWorldId: worldId }),
  updateSettings: (newSettings: Partial<GameSettings>) =>
    set((state) => ({
      settings: { ...state.settings, ...newSettings }
    })),
  setPlayerLocationPresent: (present: boolean) => set({ playerLocationPresent: present }),
  setCurrentLocation: (location: Location | null) => set({ currentLocation: location }),  // Сеттер
  setLocationLoading: (loading: boolean) => set({ locationLoading: loading }),  // Сеттер
  setGameTime: (time: GameTime) => {
    set({ gameTime: time })
    
    // Синхронизируем время с currentWorld для сохранения
    const state = get()
    if (state.currentWorld) {
      const updatedWorld = {
        ...state.currentWorld,
        parameters: {
          ...state.currentWorld.parameters,
          currentTime: time
        }
      }
      set({ currentWorld: updatedWorld })
    }
  },  // Сеттер для времени с синхронизацией
  
  // Метод для обновления позиции игрока в локации (без тумана войны)
  updatePlayerPositionInLocation: (newPosition: Position) => {
    const state = get()
    if (!state.currentLocation || !state.currentWorld?.player) return
    
    // Простое обновление позиции без тумана войны
    set({ 
      currentLocation: {
        ...state.currentLocation,
        playerPosition: { ...newPosition }
      }
    })
    
    // ТАКЖЕ обновляем позицию игрока в currentWorld для анимации!
    if (state.currentWorld?.player) {
      const updatedWorld = {
        ...state.currentWorld,
        player: {
          ...state.currentWorld.player,
          position: { ...newPosition }
        }
      }
      set({ currentWorld: updatedWorld })
    }
  },
  
  // Синхронизирует currentLocation с currentWorld перед сохранением
  syncLocationToWorld: async () => {
    if (!get().currentLocation) {
      return
    }
    
    const player = get().currentWorld?.player
    if (!player) {
      return
    }
    
    const locationKey = `${player.position.x},${player.position.y}`
    
    if (!get().currentWorld?.worldMap) {
      return
    }
    
    const worldMapCell = get().currentWorld!.worldMap[locationKey]
    if (!worldMapCell) {
      return
    }
    
    try {
      // Динамический импорт конвертера
  const { locationToTransferLocation } = await import('../systems/location/locationConverter')
      
      // Получаем актуальное состояние currentLocation
      const currentLocationState = get().currentLocation!
      
      // Конвертируем currentLocation в TransferLocation
      const transferLocation = locationToTransferLocation(currentLocationState)
      
      // Сохраняем в worldMap ячейку
      set(state => ({
        currentWorld: {
          ...state.currentWorld!,
          worldMap: {
            ...state.currentWorld!.worldMap,
            [locationKey]: {
              ...worldMapCell,
              location: transferLocation
            }
          }
        }
      }))
      
    } catch (error) {
      console.error('❌ syncLocationToWorld: Ошибка при синхронизации:', error)
    }
  },
  loadFullWorld: async (worldId: string, userId: string) => {
    try {
  const { getWorldById } = await import('../../api/worldsApi')
      const result = await getWorldById(worldId, userId)
      if (result.success && result.world) {
        // Убеждаемся, что у игрока есть позиция
        let world = result.world
        if (!world.player?.position) {
          const mapSize = world.settings?.worldSize || 20
          const centerPosition = { x: Math.floor(mapSize / 2), y: Math.floor(mapSize / 2) }
          
          if (world.player) {
            world = {
              ...world,
              player: {
                ...world.player,
                position: centerPosition
              }
            }
          }
        }
        
        // Убеждаемся, что showPath включен и worldMapPlayerSpeed установлен
        if (world.settings && (!world.settings.showPath || !world.settings.worldMapPlayerSpeed)) {
          world = {
            ...world,
            settings: {
              ...world.settings,
              showPath: true,
              worldMapPlayerSpeed: world.settings.worldMapPlayerSpeed || 3
            }
          }
        }
        
        // Загружаем время из сохранения и синхронизируем с менеджером времени
        if (world.parameters?.currentTime) {
          const savedTime = world.parameters.currentTime
          gameTimeManager.setTime(savedTime)
          set({ gameTime: savedTime })
        }
        
        // НОВАЯ ЛОГИКА: Проверяем, находится ли игрок в локации при загрузке
        let playerInLocation = false
        let foundLocation: Location | null = null
        
        if (world.worldMap && world.player?.position) {
          // Проходим по всем клеткам мира и ищем локации с playerPresent: true
          for (const [tileKey, tileRaw] of Object.entries(world.worldMap)) {
            const tile = tileRaw as any; // TODO: use correct type if available
            if (tile.location?.playerPresent) {
              playerInLocation = true
              // Конвертируем найденную локацию в Location и сохраняем в стейт
              const { transferLocationToLocation } = await import('../systems/location/locationConverter')
              foundLocation = transferLocationToLocation(tile.location)
              break
            }
          }
        }
        
        set({
          currentWorld: world,
          currentWorldId: worldId,
          playerLocationPresent: playerInLocation,  // Устанавливаем правильное состояние
          currentLocation: foundLocation  // Сохраняем конвертированную локацию в стейт
        })
      }
    } catch (error) {
      console.error('Ошибка загрузки мира:', error)
    }
  }
}))

/**
 * Система передвижения персонажа в локациях
 */

import { Position } from '../../../shared/types/Common';
import { Location, LocationMapCell } from '../../../shared/types/Location';
import { useGameStore } from '../../store/gameStore';
import { LOCATION_SPEED_MULTIPLIER } from '../../utils/constants/movement';

/**
 * Перемещает персонажа по массиву пути в локации с задержкой между шагами.
 * Скорость увеличена в 1.5 раза для синхронизации с масштабом локации.
 */
export function movePlayerByPathInLocation(
  startPosition: Position,
  path: Position[],
  speed: number = 1000 
): void {
  
  // Блок повторного запуска - если уже движется, не запускаем
  if (window.__playerIsMoving) {
    return;
  }
  
  // Применяем множитель для локации (ускоряем в 1.5 раза)
  const locationSpeed = Math.round(speed / LOCATION_SPEED_MULTIPLIER);
  
  let currentIdx = 0;
  let currentPos = { ...startPosition };
  let intervalId: NodeJS.Timeout | null = null;
  let stopped = false;
  window.__playerIsMoving = true;

  function stopMove() {
    if (intervalId) clearInterval(intervalId);
    window.removeEventListener('keydown', stopMove);
    stopped = true;
    window.__playerIsMoving = false;
    
    // Мгновенное удаление пути по завершению
    import('../movement/playerMovement').then(({ clearPath }) => {
      clearPath();
    });
  }

  window.addEventListener('keydown', stopMove);

  intervalId = setInterval(() => {
    if (stopped) return;
    if (currentIdx >= path.length) {
      stopMove();
      return;
    }
    
    currentPos = { ...path[currentIdx] };
    
    // Обновляем позицию игрока на каждом шаге
    const { updatePlayerPositionInLocation } = useGameStore.getState();
    updatePlayerPositionInLocation(currentPos);
    
    currentIdx++;
    if (currentIdx >= path.length) {
      window.__playerIsMoving = false;
    }
  }, locationSpeed);
}

/**
 * Проверяет, можно ли пройти через клетку в локации
 */
function isLocationTilePassable(locationMap: Record<string, LocationMapCell>, position: Position): boolean {
  const tileKey = `${position.x},${position.y}`;
  const tile = locationMap[tileKey];
  
  if (!tile) return false; // Если клетки нет, считаем непроходимой
  
  // Проверяем blocked флаг
  if (tile.blocked) {
    return false;
  }
  
  return true;
}

/**
 * Получает соседние клетки для алгоритма поиска пути в локации
 */
function getLocationNeighbors(position: Position, locationSize: { x: number; y: number }): Position[] {
  const neighbors: Position[] = [];
  const directions = [
    { x: -1, y: 0 },  // Запад
    { x: 1, y: 0 },   // Восток
    { x: 0, y: -1 },  // Север
    { x: 0, y: 1 },   // Юг
    { x: -1, y: -1 }, // Северо-запад
    { x: 1, y: -1 },  // Северо-восток
    { x: -1, y: 1 },  // Юго-запад
    { x: 1, y: 1 }    // Юго-восток
  ];
  
  for (const dir of directions) {
    const newPos = { x: position.x + dir.x, y: position.y + dir.y };
    if (isValidLocationPosition(newPos, locationSize)) {
      neighbors.push(newPos);
    }
  }
  
  return neighbors;
}

/**
 * Проверяет валидность позиции в локации
 */
function isValidLocationPosition(position: Position, locationSize: { x: number; y: number }): boolean {
  return position.x >= 0 && position.x < locationSize.x && 
         position.y >= 0 && position.y < locationSize.y;
}

/**
 * Вычисляет эвристическое расстояние между двумя точками (манхэттенское расстояние)
 */
function heuristic(a: Position, b: Position): number {
  return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
}

/**
 * Восстанавливает путь из карты предшественников
 */
function reconstructLocationPath(cameFrom: Map<string, Position>, current: Position): Position[] {
  const path: Position[] = [];
  let currentPos = current;
  
  while (cameFrom.has(`${currentPos.x},${currentPos.y}`)) {
    path.unshift(currentPos);
    currentPos = cameFrom.get(`${currentPos.x},${currentPos.y}`)!;
  }
  
  return path;
}

/**
 * Функция для вычисления кратчайшего пути до выбранной точки в локации
 * Использует алгоритм A* для поиска пути с учетом препятствий в локации
 */
export function calculateLocationPath(
  location: Location,
  playerPosition: Position,
  targetPosition: Position
): Position[] {
  const locationSize = location.locationSize || { x: 10, y: 10 };
  
  // Проверяем валидность координат
  if (!isValidLocationPosition(playerPosition, locationSize) || 
      !isValidLocationPosition(targetPosition, locationSize) || 
      window.__playerIsMoving) {
    return [];
  }

  // Если игрок уже на целевой позиции
  if (playerPosition.x === targetPosition.x && playerPosition.y === targetPosition.y) {
    return [];
  }

  // Проверяем, можно ли дойти до целевой позиции
  if (!isLocationTilePassable(location.locationMap, targetPosition)) {
    return [];
  }

  // Алгоритм A*
  const openSet = new Set<string>();
  const closedSet = new Set<string>();
  const cameFrom = new Map<string, Position>();
  const gScore = new Map<string, number>();
  const fScore = new Map<string, number>();

  const startKey = `${playerPosition.x},${playerPosition.y}`;
  const targetKey = `${targetPosition.x},${targetPosition.y}`;

  openSet.add(startKey);
  gScore.set(startKey, 0);
  fScore.set(startKey, heuristic(playerPosition, targetPosition));

  while (openSet.size > 0) {
    // Находим узел с наименьшим fScore
    let current = '';
    let lowestFScore = Infinity;
    
    for (const node of openSet) {
      const score = fScore.get(node) || Infinity;
      if (score < lowestFScore) {
        lowestFScore = score;
        current = node;
      }
    }

    if (current === targetKey) {
      // Путь найден!
      const [x, y] = current.split(',').map(Number);
      const path = reconstructLocationPath(cameFrom, { x, y });
      return path;
    }

    openSet.delete(current);
    closedSet.add(current);

    const [currentX, currentY] = current.split(',').map(Number);
    const currentPos = { x: currentX, y: currentY };
    const neighbors = getLocationNeighbors(currentPos, locationSize);

    for (const neighbor of neighbors) {
      const neighborKey = `${neighbor.x},${neighbor.y}`;
      
      if (closedSet.has(neighborKey)) continue;
      if (!isLocationTilePassable(location.locationMap, neighbor)) continue;

      // Все проходимые клетки имеют одинаковую стоимость = 1 (нет movementCost в LocationMapCell)
      const movementCost = 1;
      const tentativeGScore = (gScore.get(current) || 0) + movementCost;

      if (!openSet.has(neighborKey)) {
        openSet.add(neighborKey);
      } else if (tentativeGScore >= (gScore.get(neighborKey) || Infinity)) {
        continue;
      }

      cameFrom.set(neighborKey, currentPos);
      gScore.set(neighborKey, tentativeGScore);
      fScore.set(neighborKey, tentativeGScore + heuristic(neighbor, targetPosition));
    }
  }

  return [];
}

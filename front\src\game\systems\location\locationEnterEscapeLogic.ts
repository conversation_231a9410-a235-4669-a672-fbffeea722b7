/**
 * Система логики входа и выхода из локаций
 */

import { WorldMap } from '../../../shared/types/World';
import { Location } from '../../../shared/types/Location';
import { Position } from '../../../shared/types/Common';
import { transferLocationToLocation, locationToTransferLocation } from './locationConverter';
import { clearPath } from '../movement/playerMovement';

/**
 * Вход в локацию (асинхронная версия с индикатором загрузки)
 */
export async function enterLocationAsync(
  currentWorld: WorldMap,
  playerPosition: Position
): Promise<WorldMap | null> {
  // Включаем индикатор загрузки
  const { useGameStore } = await import('../../store/gameStore');
  const { setLocationLoading } = useGameStore.getState();
  setLocationLoading(true);

  try {
    // Вызываем синхронную функцию в setTimeout для не блокирования UI
    return await new Promise<WorldMap | null>((resolve) => {
      setTimeout(() => {
        const result = enterLocation(currentWorld, playerPosition);
        resolve(result);
      }, 50); // Небольшая задержка для показа загрузки
    });
  } finally {
    // Выключаем индикатор загрузки
    setLocationLoading(false);
  }
}

/**
 * Вход в локацию
 */
export function enterLocation(
  currentWorld: WorldMap,
  playerPosition: Position
): WorldMap | null {
  const tileKey = `${playerPosition.x},${playerPosition.y}`;
  const currentTile = currentWorld.worldMap?.[tileKey];
  
  if (!currentTile?.location) {
    return null; // Нет локации на этой клетке
  }

  // Конвертируем TransferLocation в Location для работы
  const location: Location = transferLocationToLocation(currentTile.location);

  // Находим спавн зону в локации
  let spawnPosition: Position | null = null;
  
  for (const [locationTileKey, locationTile] of Object.entries(location.locationMap)) {
    if (locationTile.spawnZone) {
      const [x, y] = locationTileKey.split(',').map(Number);
      spawnPosition = { x, y };
      break;
    }
  }

  // Если нет спавн зоны, используем центр локации
  if (!spawnPosition) {
    const locationSize = location.locationSize || { x: 10, y: 10 };
    spawnPosition = { x: Math.floor(locationSize.x / 2), y: Math.floor(locationSize.y / 2) };
  }

  // Обновляем локацию
  const updatedLocation: Location = {
    ...location,
    playerPresent: true,
    playerPosition: spawnPosition
  };

  // Сохраняем Location в стейт для работы во время игры (без тумана войны)
  import('../../store/gameStore').then(({ useGameStore }) => {
    useGameStore.getState().setCurrentLocation(updatedLocation);
    
    // СИНХРОНИЗИРУЕМ: конвертируем локацию в transferLocation и сохраняем в worldMap
    import('./locationConverter').then(({ locationToTransferLocation }) => {
      const updatedTransferLocation = locationToTransferLocation(updatedLocation);
      
      // Обновляем локацию в worldMap
      const currentState = useGameStore.getState();
      if (currentState.currentWorld?.worldMap) {
        const updatedWorldMap = { ...currentState.currentWorld.worldMap };
        if (updatedWorldMap[tileKey]?.location) {
          updatedWorldMap[tileKey] = {
            ...updatedWorldMap[tileKey],
            location: updatedTransferLocation
          };
          
          // Обновляем мир в сторе
          useGameStore.getState().setCurrentWorld({
            ...currentState.currentWorld,
            worldMap: updatedWorldMap
          });
          
        }
      }
    });
  });

  // ОБНОВЛЯЕМ playerPresent в TransferLocation в worldMap
  const updatedWorldMap = { ...currentWorld.worldMap };
  if (updatedWorldMap[tileKey]?.location) {
    updatedWorldMap[tileKey] = {
      ...updatedWorldMap[tileKey],
      location: {
        ...updatedWorldMap[tileKey].location!,
        playerPresent: true
      }
    };
  }
  
  return {
    ...currentWorld,
    worldMap: updatedWorldMap,
    player: currentWorld.player ? {
      ...currentWorld.player,
      position: spawnPosition
    } : undefined
  };
}

/**
 * Обновление позиции игрока в локации при движении
 */
export function updatePlayerPositionInLocation(
  currentWorld: WorldMap,
  newPlayerPosition: Position
): WorldMap {
  // Обновляем позицию в currentLocation в стейте, а НЕ в worldMap
  import('../../store/gameStore').then(({ useGameStore }) => {
    const { currentLocation, setCurrentLocation } = useGameStore.getState();
    if (currentLocation) {
      const updatedLocation: Location = {
        ...currentLocation,
        playerPosition: newPlayerPosition
      };
      setCurrentLocation(updatedLocation);
    }
  });

  return {
    ...currentWorld,
    player: currentWorld.player ? {
      ...currentWorld.player,
      position: newPlayerPosition
    } : undefined
  };
}

/**
 * Выход из локации (асинхронная версия с индикатором загрузки)
 */
export async function exitLocationAsync(
  currentWorld: WorldMap,
  playerPosition: Position,
  currentLocation: Location | null = null
): Promise<{ success: boolean; updatedWorld?: WorldMap; worldPosition?: Position }> {
  // Включаем индикатор загрузки
  const { useGameStore } = await import('../../store/gameStore');
  const { setLocationLoading } = useGameStore.getState();
  setLocationLoading(true);

  try {
    // СНАЧАЛА синхронизируем currentLocation с worldMap
    await useGameStore.getState().syncLocationToWorld();
    
    // Вызываем синхронную функцию в setTimeout для не блокирования UI
    return await new Promise<{ success: boolean; updatedWorld?: WorldMap; worldPosition?: Position }>((resolve) => {
      setTimeout(() => {
        const result = exitLocation(currentWorld, playerPosition, currentLocation);
        resolve(result);
      }, 50); // Небольшая задержка для показа загрузки
    });
  } finally {
    // Выключаем индикатор загрузки
    setLocationLoading(false);
  }
}

/**
 * Выход из локации
 */
export function exitLocation(
  currentWorld: WorldMap,
  playerPosition: Position,
  currentLocation: Location | null = null
): { success: boolean; updatedWorld?: WorldMap; worldPosition?: Position } {
  // Находим локацию в мире и позицию игрока
  let locationTileKey: string | null = null;
  let worldPosition: Position | null = null;

  for (const [tileKey, tile] of Object.entries(currentWorld.worldMap)) {
    if (tile.location?.playerPresent) {
      locationTileKey = tileKey;
      const [x, y] = tileKey.split(',').map(Number);
      worldPosition = { x, y };
      break;
    }
  }

  if (!locationTileKey || !worldPosition) {
    return { success: false }; // Игрок не в локации
  }

  if (!currentLocation) {
    return { success: false }; // Нет активной локации в стейте
  }

  // Проверяем, находится ли игрок на клетке с goBackZone
  const currentLocationTileKey = `${playerPosition.x},${playerPosition.y}`;
  const currentLocationTile = currentLocation.locationMap?.[currentLocationTileKey];
  if (!currentLocationTile?.goBackZone) {
    return { success: false }; // Игрок не на клетке выхода
  }

  // Конвертируем Location обратно в TransferLocation для сохранения в мир
  const updatedLocation: Location = {
    ...currentLocation,
    playerPresent: false
  };
  const transferLocation = locationToTransferLocation(updatedLocation);

  // Обновляем мир - сохраняем обновленную TransferLocation
  const updatedWorldMap = { ...currentWorld.worldMap };
  const currentTile = currentWorld.worldMap?.[locationTileKey];
  if (currentTile) {
    updatedWorldMap[locationTileKey] = {
      ...currentTile,
      location: transferLocation
    };
  }

  const updatedWorld = {
    ...currentWorld,
    worldMap: updatedWorldMap,
    player: currentWorld.player ? {
      ...currentWorld.player,
      position: worldPosition
    } : undefined
  };

  return { 
    success: true, 
    updatedWorld,
    worldPosition 
  };
}

/**
 * Проверяет, может ли игрок выйти из локации с текущей позиции
 */
export function canExitLocation(
  currentWorld: WorldMap,
): boolean {
  
  // Находим локацию, где находится игрок
  for (const [tileKey, tile] of Object.entries(currentWorld.worldMap)) {
   
    
    if (tile.location?.playerPresent) {
      // Конвертируем в Location для проверки
      const location: Location = transferLocationToLocation(tile.location);
      // ИСПОЛЬЗУЕМ позицию игрока ИЗ ЛОКАЦИИ, а не мировую позицию!
      const locationPlayerPosition = location.playerPosition;
      if (!locationPlayerPosition) {
        return false;
      }
      
      // Проверяем, находится ли игрок на клетке с goBackZone
      const currentLocationTileKey = `${locationPlayerPosition.x},${locationPlayerPosition.y}`;
      const currentLocationTile = location.locationMap?.[currentLocationTileKey];
      
      return !!currentLocationTile?.goBackZone;
    }
  }

  return false;
}